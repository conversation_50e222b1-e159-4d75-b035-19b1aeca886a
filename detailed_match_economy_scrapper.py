#!/usr/bin/env python3
"""
Detailed Match Economy Scraper for VLR.gg
Scrapes the economy tab data including economic statistics and round-by-round economy information
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from datetime import datetime
import re

class DetailedMatchEconomyScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def get_match_economy_data(self, match_url):
        """
        Scrape detailed economy data from a VLR match page
        
        Args:
            match_url (str): URL of the match (e.g., https://www.vlr.gg/378822/drx-vs-sentinels-valorant-champions-2024-ubqf)
            
        Returns:
            dict: Economy data with player and team economic statistics
        """
        try:
            # Add economy tab parameter to URL with game=all to get aggregate data
            if '?' in match_url:
                economy_url = f"{match_url}&game=all&tab=economy"
            else:
                economy_url = f"{match_url}?game=all&tab=economy"
            
            print(f"💰 Scraping economy data from: {economy_url}")
            
            # Add random delay to avoid rate limiting
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(economy_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract match basic info
            match_info = self._extract_match_info(soup)
            
            # Extract economy data for each map
            economy_data = self._extract_economy_data(soup)
            
            result = {
                'match_url': match_url,
                'economy_url': economy_url,
                'scraped_at': datetime.now().isoformat(),
                'match_info': match_info,
                'economy_data': economy_data
            }
            
            return result
            
        except Exception as e:
            print(f"❌ Error scraping economy data: {e}")
            return None
    
    def _extract_match_info(self, soup):
        """Extract basic match information"""
        try:
            match_info = {}
            
            # Extract team names
            team_elements = soup.find_all('div', class_='match-header-vs-team')
            if len(team_elements) >= 2:
                match_info['team1'] = team_elements[0].get_text(strip=True)
                match_info['team2'] = team_elements[1].get_text(strip=True)
            
            # Extract event name
            event_element = soup.find('div', class_='match-header-event')
            if event_element:
                match_info['event'] = event_element.get_text(strip=True)
            
            # Extract match date
            date_element = soup.find('div', class_='moment-tz-convert')
            if date_element:
                match_info['date'] = date_element.get('data-utc-ts', '')
            
            return match_info
            
        except Exception as e:
            print(f"⚠️ Error extracting match info: {e}")
            return {}
    
    def _extract_economy_data(self, soup):
        """Extract detailed economy statistics from team-level data"""
        try:
            economy_data = {}

            # Find all tables with economy data
            tables = soup.find_all('table')

            # Look for tables with economy headers
            for table_idx, table in enumerate(tables):
                headers = table.find_all('th')
                if not headers:
                    continue

                header_texts = [th.get_text(strip=True) for th in headers]

                # Look for economy table headers (Pistol Won, Eco, $, $$, $$$)
                if 'Pistol Won' in header_texts or 'Eco' in header_texts:
                    print(f"💰 Found economy table with headers: {header_texts}")

                    # Extract team economy data from this table
                    team_economy = self._extract_team_economy_data(table)

                    if team_economy:
                        economy_data[f'map_{table_idx + 1}'] = {
                            'map_name': f'Map {table_idx + 1}',
                            'team_economy': team_economy,
                            'economy_type': 'team_level'
                        }

            # If no team economy data found, create aggregate data
            if not economy_data:
                economy_data['map_1'] = {
                    'map_name': 'All Maps (Aggregate)',
                    'team_economy': {'team1': {}, 'team2': {}},
                    'economy_type': 'team_level'
                }

            return economy_data

        except Exception as e:
            print(f"⚠️ Error extracting economy data: {e}")
            return {}

    def _extract_team_economy_data(self, table):
        """Extract team-level economy data from a table"""
        try:
            team_data = {'team1': {}, 'team2': {}}

            # Find all data rows (skip header row)
            rows = table.find_all('tr')

            for row in rows:
                cells = row.find_all('td')

                # Skip rows without enough cells
                if len(cells) < 2:
                    continue

                # Extract team name from first cell
                team_name = cells[0].get_text(strip=True)
                if not team_name:
                    continue

                # Extract economy metrics
                economy_metrics = {}
                headers = table.find_all('th')
                header_texts = [th.get_text(strip=True) for th in headers]

                for i, cell in enumerate(cells[1:], 1):  # Skip team name cell
                    if i < len(header_texts):
                        metric_name = header_texts[i]
                        metric_value = cell.get_text(strip=True)
                        economy_metrics[metric_name] = metric_value

                # Assign to team1 or team2 based on order
                if not team_data['team1']:
                    team_data['team1'] = {
                        'team_name': team_name,
                        'metrics': economy_metrics
                    }
                elif not team_data['team2']:
                    team_data['team2'] = {
                        'team_name': team_name,
                        'metrics': economy_metrics
                    }
                    break  # We have both teams

            return team_data

        except Exception as e:
            print(f"⚠️ Error extracting team economy data: {e}")
            return {'team1': {}, 'team2': {}}
    
    def _extract_map_name(self, map_container):
        """Extract map name from container"""
        try:
            map_element = map_container.find('div', class_='map')
            if map_element:
                map_name = map_element.get_text(strip=True)
                return map_name
            return "Unknown Map"
        except:
            return "Unknown Map"
    
    def _extract_economy_stats(self, map_container):
        """Extract player economy statistics"""
        try:
            economy_stats = {
                'team1_players': [],
                'team2_players': []
            }
            
            # Find the economy stats table
            stats_table = map_container.find('table', class_='wf-table-inset')
            if not stats_table:
                return economy_stats
            
            # Find all player rows
            player_rows = stats_table.find_all('tr')
            
            current_team = 'team1_players'
            
            for row in player_rows:
                # Check if this is a team separator
                if 'mod-color' in row.get('class', []):
                    current_team = 'team2_players' if current_team == 'team1_players' else 'team1_players'
                    continue
                
                # Extract player economy data
                player_data = self._extract_player_economy_row(row)
                if player_data:
                    economy_stats[current_team].append(player_data)
            
            return economy_stats
            
        except Exception as e:
            print(f"⚠️ Error extracting economy stats: {e}")
            return {'team1_players': [], 'team2_players': []}
    
    def _extract_player_economy_row(self, row):
        """Extract economy data from a player row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 8:  # Need at least 8 columns for economy data
                return None
            
            # Extract player name and agent
            player_cell = cells[0]
            player_name = player_cell.get_text(strip=True)
            
            # Extract agent from image
            agent_img = player_cell.find('img')
            agent = agent_img.get('alt', '') if agent_img else ''
            
            # Extract economy statistics
            # Common economy columns: Player, Agent, Credits Earned, Credits Spent, Equipment Value, Loadout Value, Spent/Round, Damage/Credit
            economy_data = {
                'player_name': player_name,
                'agent': agent,
                'credits_earned': self._safe_extract_number(cells[2]),
                'credits_spent': self._safe_extract_number(cells[3]),
                'equipment_value': self._safe_extract_number(cells[4]),
                'loadout_value': self._safe_extract_number(cells[5]),
                'spent_per_round': self._safe_extract_number(cells[6]),
                'damage_per_credit': self._safe_extract_number(cells[7]) if len(cells) > 7 else 0,
                'economy_rating': self._safe_extract_number(cells[8]) if len(cells) > 8 else 0
            }
            
            return economy_data
            
        except Exception as e:
            print(f"⚠️ Error extracting player economy row: {e}")
            return None
    
    def _extract_round_economy(self, map_container):
        """Extract round-by-round economy information"""
        try:
            round_economy = []
            
            # Find round economy containers
            round_containers = map_container.find_all('div', class_='vlr-rounds-row')
            
            for round_idx, round_container in enumerate(round_containers):
                round_data = {
                    'round_number': round_idx + 1,
                    'team1_economy': self._extract_team_round_economy(round_container, 'team1'),
                    'team2_economy': self._extract_team_round_economy(round_container, 'team2')
                }
                round_economy.append(round_data)
            
            return round_economy
            
        except Exception as e:
            print(f"⚠️ Error extracting round economy: {e}")
            return []
    
    def _extract_team_round_economy(self, round_container, team):
        """Extract economy data for a team in a specific round"""
        try:
            team_economy = {
                'total_credits': 0,
                'credits_spent': 0,
                'buy_type': 'unknown',  # full-buy, eco, force-buy, etc.
                'weapons': [],
                'utilities': []
            }
            
            # Find team-specific economy elements
            team_elements = round_container.find_all('div', class_=f'{team}-economy')
            
            for element in team_elements:
                # Extract credit information
                credit_element = element.find('span', class_='credits')
                if credit_element:
                    team_economy['total_credits'] = self._safe_extract_number(credit_element)
                
                # Extract buy type
                buy_type_element = element.find('span', class_='buy-type')
                if buy_type_element:
                    team_economy['buy_type'] = buy_type_element.get_text(strip=True).lower()
                
                # Extract weapons and utilities
                weapon_elements = element.find_all('img', class_='weapon-icon')
                for weapon_img in weapon_elements:
                    weapon_name = weapon_img.get('alt', '')
                    if weapon_name:
                        team_economy['weapons'].append(weapon_name)
                
                utility_elements = element.find_all('img', class_='utility-icon')
                for utility_img in utility_elements:
                    utility_name = utility_img.get('alt', '')
                    if utility_name:
                        team_economy['utilities'].append(utility_name)
            
            return team_economy
            
        except Exception as e:
            print(f"⚠️ Error extracting team round economy: {e}")
            return {'total_credits': 0, 'credits_spent': 0, 'buy_type': 'unknown', 'weapons': [], 'utilities': []}
    
    def _safe_extract_number(self, cell):
        """Safely extract number from table cell"""
        try:
            text = cell.get_text(strip=True)
            # Remove currency symbols and commas
            text = re.sub(r'[,$]', '', text)
            # Handle percentage values
            if '%' in text:
                return text
            # Handle numeric values
            if text.replace('.', '').isdigit():
                return float(text) if '.' in text else int(text)
            return text if text else 0
        except:
            return 0
    
    def save_economy_data(self, economy_data, filename=None):
        """Save economy data to JSON file"""
        try:
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"match_economy_data_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(economy_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Economy data saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error saving economy data: {e}")
            return None

def main():
    """Test the economy scraper"""
    scraper = DetailedMatchEconomyScraper()
    
    # Test URL
    test_url = "https://www.vlr.gg/378822/drx-vs-sentinels-valorant-champions-2024-ubqf"
    
    print("💰 VLR Detailed Match Economy Scraper")
    print("=" * 60)
    
    # Scrape economy data
    economy_data = scraper.get_match_economy_data(test_url)
    
    if economy_data:
        print(f"\n✅ Successfully scraped economy data!")
        print(f"📊 Match: {economy_data.get('match_info', {}).get('team1', 'Team1')} vs {economy_data.get('match_info', {}).get('team2', 'Team2')}")
        print(f"🗺️ Maps found: {len(economy_data.get('economy_data', {}))}")
        
        # Save data
        filename = scraper.save_economy_data(economy_data)
        
        # Display sample data
        print(f"\n📋 Sample Economy Data:")
        for map_key, map_data in economy_data.get('economy_data', {}).items():
            print(f"  🗺️ {map_data.get('map_name', 'Unknown')}")
            team1_count = len(map_data.get('economy_stats', {}).get('team1_players', []))
            team2_count = len(map_data.get('economy_stats', {}).get('team2_players', []))
            print(f"    👥 Team 1: {team1_count} players, Team 2: {team2_count} players")
            
            # Show sample player data
            if team1_count > 0:
                sample_player = map_data['economy_stats']['team1_players'][0]
                print(f"    💰 Sample: {sample_player.get('player_name', 'Unknown')} - Credits Earned: {sample_player.get('credits_earned', 0)}, Spent: {sample_player.get('credits_spent', 0)}")
            
            # Show round economy info
            round_count = len(map_data.get('round_economy', []))
            print(f"    🔄 Rounds tracked: {round_count}")
        
        print(f"\n🎯 Economy scraping complete!")
        
    else:
        print(f"\n❌ Failed to scrape economy data")

if __name__ == "__main__":
    main()
