#!/usr/bin/env python3
"""
Detailed Match Performance Scraper for VLR.gg (Version 2)
Scrapes the performance tab data including 2K, 3K, 4K, 5K, 1v1-1v5 clutches, ECON, PL, DE
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from datetime import datetime
import re

class DetailedMatchPerformanceScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def get_match_performance_data(self, match_url):
        """
        Scrape detailed performance data from a VLR match page
        
        Args:
            match_url (str): URL of the match
            
        Returns:
            dict: Performance data with player statistics
        """
        try:
            # Add performance tab parameter to URL with game=all to get aggregate data
            if '?' in match_url:
                performance_url = f"{match_url}&game=all&tab=performance"
            else:
                performance_url = f"{match_url}?game=all&tab=performance"
            
            print(f"🎯 Scraping performance data from: {performance_url}")
            
            # Add random delay to avoid rate limiting
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(performance_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract match basic info
            match_info = self._extract_match_info(soup)
            
            # Extract performance data
            performance_data = self._extract_performance_data(soup)
            
            result = {
                'match_url': match_url,
                'performance_url': performance_url,
                'scraped_at': datetime.now().isoformat(),
                'match_info': match_info,
                'performance_data': performance_data
            }
            
            return result
            
        except Exception as e:
            print(f"❌ Error scraping performance data: {e}")
            return None
    
    def _extract_match_info(self, soup):
        """Extract basic match information"""
        try:
            match_info = {}
            
            # Extract team names from the page title or headers
            title = soup.find('title')
            if title:
                title_text = title.get_text()
                if ' vs ' in title_text:
                    teams = title_text.split(' vs ')[0:2]
                    if len(teams) >= 2:
                        match_info['team1'] = teams[0].strip()
                        match_info['team2'] = teams[1].split(' |')[0].strip()
            
            return match_info
            
        except Exception as e:
            print(f"⚠️ Error extracting match info: {e}")
            return {}
    
    def _extract_performance_data(self, soup):
        """Extract detailed performance statistics from the aggregate 'All Maps' table"""
        try:
            performance_data = {}

            # Find all tables with performance headers (2K, 3K, 4K, 5K, etc.)
            tables = soup.find_all('table')

            for table in tables:
                # Check if this table has performance headers
                headers = table.find_all('th')
                if not headers:
                    continue

                header_texts = [th.get_text(strip=True) for th in headers]

                # Look for performance table headers
                if '2K' in header_texts and '3K' in header_texts and '4K' in header_texts and '5K' in header_texts:
                    print(f"🎯 Found performance table with headers: {header_texts}")

                    # Extract data from this performance table
                    team1_players, team2_players = self._extract_performance_table_data(table)

                    # Only use the first valid performance table (should be the aggregate)
                    if team1_players or team2_players:
                        performance_data['map_1'] = {
                            'map_name': 'All Maps (Aggregate)',
                            'team1_players': team1_players,
                            'team2_players': team2_players
                        }
                        break  # Use only the first performance table found

            return performance_data

        except Exception as e:
            print(f"⚠️ Error extracting performance data: {e}")
            return {}

    def _extract_performance_table_data(self, table):
        """Extract performance data from a specific table"""
        try:
            team1_players = []
            team2_players = []
            current_team = 1

            # Find all data rows (skip header row)
            rows = table.find_all('tr')

            for row in rows:
                cells = row.find_all('td')

                # Skip rows without enough cells
                if len(cells) < 8:  # Need at least player name + some performance data
                    continue

                # Extract player performance data
                player_data = self._extract_performance_row_from_cells(cells)
                if player_data:
                    # Simple team detection: first 5 players go to team1, next 5 to team2
                    if len(team1_players) < 5:
                        team1_players.append(player_data)
                    elif len(team2_players) < 5:
                        team2_players.append(player_data)
                    else:
                        break  # We have enough players for both teams

            return team1_players, team2_players

        except Exception as e:
            print(f"⚠️ Error extracting performance table data: {e}")
            return [], []

    def _extract_performance_row_from_cells(self, cells):
        """Extract performance data from table cells"""
        try:
            if len(cells) < 8:
                return None

            # Extract player name (first cell)
            player_name = cells[0].get_text(strip=True)
            if not player_name:
                return None

            # Extract agent (look for agent image or text in first cell)
            agent = ''
            agent_img = cells[0].find('img')
            if agent_img:
                agent = agent_img.get('alt', '')

            # Extract performance statistics based on expected column positions
            # Expected columns: Player, Agent, 2K, 3K, 4K, 5K, 1v1, 1v2, 1v3, 1v4, 1v5, ECON, PL, DE
            performance_data = {
                'player_name': player_name,
                'agent': agent,
                'multikills': {
                    '2k': self._safe_extract_number(cells[2]) if len(cells) > 2 else 0,
                    '3k': self._safe_extract_number(cells[3]) if len(cells) > 3 else 0,
                    '4k': self._safe_extract_number(cells[4]) if len(cells) > 4 else 0,
                    '5k': self._safe_extract_number(cells[5]) if len(cells) > 5 else 0
                },
                'clutches': {
                    '1v1': self._safe_extract_number(cells[6]) if len(cells) > 6 else 0,
                    '1v2': self._safe_extract_number(cells[7]) if len(cells) > 7 else 0,
                    '1v3': self._safe_extract_number(cells[8]) if len(cells) > 8 else 0,
                    '1v4': self._safe_extract_number(cells[9]) if len(cells) > 9 else 0,
                    '1v5': self._safe_extract_number(cells[10]) if len(cells) > 10 else 0
                },
                'other_stats': {
                    'econ': self._safe_extract_number(cells[11]) if len(cells) > 11 else 0,
                    'pl': self._safe_extract_number(cells[12]) if len(cells) > 12 else 0,
                    'de': self._safe_extract_number(cells[13]) if len(cells) > 13 else 0
                }
            }

            return performance_data

        except Exception as e:
            print(f"⚠️ Error extracting performance row from cells: {e}")
            return None

    def _extract_performance_row(self, row, cells):
        """Extract performance data from a table row"""
        try:
            # Look for rows with the performance data structure
            # The structure should have: Player, Agent, 2K, 3K, 4K, 5K, 1v1, 1v2, 1v3, 1v4, 1v5, ECON, PL, DE
            
            # Extract player name from first cell
            player_cell = cells[0]
            player_name = player_cell.get_text(strip=True)
            
            # Skip if this doesn't look like a player name
            if not player_name or len(player_name) < 2:
                return None
            
            # Extract agent from image in the first cell
            agent = ''
            agent_img = player_cell.find('img', src=lambda x: x and 'agents' in x)
            if agent_img:
                agent_src = agent_img.get('src', '')
                if 'agents' in agent_src:
                    agent = agent_src.split('/')[-1].replace('.png', '')
            
            # Extract the performance statistics
            # Look for the pattern where we have numeric data in the expected columns
            performance_data = {
                'player_name': player_name,
                'agent': agent,
                'multikills': {
                    '2k': self._safe_extract_number(cells[1]) if len(cells) > 1 else 0,
                    '3k': self._safe_extract_number(cells[2]) if len(cells) > 2 else 0,
                    '4k': self._safe_extract_number(cells[3]) if len(cells) > 3 else 0,
                    '5k': self._safe_extract_number(cells[4]) if len(cells) > 4 else 0
                },
                'clutches': {
                    '1v1': self._safe_extract_number(cells[5]) if len(cells) > 5 else 0,
                    '1v2': self._safe_extract_number(cells[6]) if len(cells) > 6 else 0,
                    '1v3': self._safe_extract_number(cells[7]) if len(cells) > 7 else 0,
                    '1v4': self._safe_extract_number(cells[8]) if len(cells) > 8 else 0,
                    '1v5': self._safe_extract_number(cells[9]) if len(cells) > 9 else 0
                },
                'other_stats': {
                    'econ': self._safe_extract_number(cells[10]) if len(cells) > 10 else 0,
                    'pl': self._safe_extract_number(cells[11]) if len(cells) > 11 else 0,
                    'de': self._safe_extract_number(cells[12]) if len(cells) > 12 else 0
                }
            }
            
            # Validate that this looks like performance data
            total_stats = (performance_data['multikills']['2k'] + 
                          performance_data['multikills']['3k'] + 
                          performance_data['clutches']['1v1'] + 
                          performance_data['other_stats']['econ'])
            
            # Only return if we have some actual performance data
            if total_stats > 0 or performance_data['agent']:
                return performance_data
            
            return None
            
        except Exception as e:
            print(f"⚠️ Error extracting performance row: {e}")
            return None
    
    def _safe_extract_number(self, cell):
        """Safely extract number from table cell"""
        try:
            text = cell.get_text(strip=True)
            # Remove any non-numeric characters except decimal points
            text = re.sub(r'[^\d.]', '', text)
            if text and text.replace('.', '').isdigit():
                return float(text) if '.' in text else int(text)
            return 0
        except:
            return 0
    
    def save_performance_data(self, performance_data, filename=None):
        """Save performance data to JSON file"""
        try:
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"match_performance_data_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(performance_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Performance data saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error saving performance data: {e}")
            return None

def main():
    """Test the performance scraper"""
    scraper = DetailedMatchPerformanceScraper()
    
    # Test URL
    test_url = "https://www.vlr.gg/378822/drx-vs-sentinels-valorant-champions-2024-ubqf"
    
    print("🎯 VLR Detailed Match Performance Scraper v2")
    print("=" * 60)
    
    # Scrape performance data
    performance_data = scraper.get_match_performance_data(test_url)
    
    if performance_data:
        print(f"\n✅ Successfully scraped performance data!")
        print(f"📊 Match: {performance_data.get('match_info', {}).get('team1', 'Team1')} vs {performance_data.get('match_info', {}).get('team2', 'Team2')}")
        
        # Display sample data
        print(f"\n📋 Performance Data Summary:")
        for map_key, map_data in performance_data.get('performance_data', {}).items():
            team1_count = len(map_data.get('team1_players', []))
            team2_count = len(map_data.get('team2_players', []))
            print(f"  🗺️ {map_data.get('map_name', 'Unknown')}")
            print(f"    👥 Team 1: {team1_count} players, Team 2: {team2_count} players")
            
            # Show sample player data
            if team1_count > 0:
                sample_player = map_data['team1_players'][0]
                print(f"    🎯 Sample: {sample_player.get('player_name', 'Unknown')} ({sample_player.get('agent', 'Unknown agent')})")
                print(f"       Multikills: 2K={sample_player['multikills']['2k']}, 3K={sample_player['multikills']['3k']}")
                print(f"       Clutches: 1v1={sample_player['clutches']['1v1']}, 1v2={sample_player['clutches']['1v2']}")
                print(f"       Other: ECON={sample_player['other_stats']['econ']}, PL={sample_player['other_stats']['pl']}, DE={sample_player['other_stats']['de']}")
        
        # Save data
        filename = scraper.save_performance_data(performance_data)
        print(f"\n🎯 Performance scraping complete!")
        
    else:
        print(f"\n❌ Failed to scrape performance data")

if __name__ == "__main__":
    main()
